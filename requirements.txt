# Vanna AI 核心依赖
vanna==0.7.9

# 向量数据库（锁定到成功运行的版本）
chromadb==0.6.3

# Web 框架
flask==3.1.1
flask-sock==0.7.0
flasgger==0.9.7.1

# 数据处理
pandas==2.2.3
numpy==2.0.2

# HTTP 请求
requests==2.32.3

# MySQL 数据库连接器
pymysql==1.1.1

# 其他必需的依赖（基于成功环境）
coloredlogs==15.0.1
python-dotenv==1.1.0
plotly==6.1.0
tabulate==0.9.0
PyYAML==6.0.2

# 基础依赖
click==8.1.8
tqdm==4.67.1
rich==14.0.0

# Python 标准库扩展（代码中实际使用的）
# json, time, re, functools, abc, uuid, hashlib, os, shutil, sys, traceback 都是标准库，无需安装 