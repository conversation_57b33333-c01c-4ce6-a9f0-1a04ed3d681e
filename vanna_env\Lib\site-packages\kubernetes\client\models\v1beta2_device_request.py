# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1beta2DeviceRequest(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'exactly': 'V1beta2ExactDeviceRequest',
        'first_available': 'list[V1beta2DeviceSubRequest]',
        'name': 'str'
    }

    attribute_map = {
        'exactly': 'exactly',
        'first_available': 'firstAvailable',
        'name': 'name'
    }

    def __init__(self, exactly=None, first_available=None, name=None, local_vars_configuration=None):  # noqa: E501
        """V1beta2DeviceRequest - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._exactly = None
        self._first_available = None
        self._name = None
        self.discriminator = None

        if exactly is not None:
            self.exactly = exactly
        if first_available is not None:
            self.first_available = first_available
        self.name = name

    @property
    def exactly(self):
        """Gets the exactly of this V1beta2DeviceRequest.  # noqa: E501


        :return: The exactly of this V1beta2DeviceRequest.  # noqa: E501
        :rtype: V1beta2ExactDeviceRequest
        """
        return self._exactly

    @exactly.setter
    def exactly(self, exactly):
        """Sets the exactly of this V1beta2DeviceRequest.


        :param exactly: The exactly of this V1beta2DeviceRequest.  # noqa: E501
        :type: V1beta2ExactDeviceRequest
        """

        self._exactly = exactly

    @property
    def first_available(self):
        """Gets the first_available of this V1beta2DeviceRequest.  # noqa: E501

        FirstAvailable contains subrequests, of which exactly one will be selected by the scheduler. It tries to satisfy them in the order in which they are listed here. So if there are two entries in the list, the scheduler will only check the second one if it determines that the first one can not be used.  DRA does not yet implement scoring, so the scheduler will select the first set of devices that satisfies all the requests in the claim. And if the requirements can be satisfied on more than one node, other scheduling features will determine which node is chosen. This means that the set of devices allocated to a claim might not be the optimal set available to the cluster. Scoring will be implemented later.  # noqa: E501

        :return: The first_available of this V1beta2DeviceRequest.  # noqa: E501
        :rtype: list[V1beta2DeviceSubRequest]
        """
        return self._first_available

    @first_available.setter
    def first_available(self, first_available):
        """Sets the first_available of this V1beta2DeviceRequest.

        FirstAvailable contains subrequests, of which exactly one will be selected by the scheduler. It tries to satisfy them in the order in which they are listed here. So if there are two entries in the list, the scheduler will only check the second one if it determines that the first one can not be used.  DRA does not yet implement scoring, so the scheduler will select the first set of devices that satisfies all the requests in the claim. And if the requirements can be satisfied on more than one node, other scheduling features will determine which node is chosen. This means that the set of devices allocated to a claim might not be the optimal set available to the cluster. Scoring will be implemented later.  # noqa: E501

        :param first_available: The first_available of this V1beta2DeviceRequest.  # noqa: E501
        :type: list[V1beta2DeviceSubRequest]
        """

        self._first_available = first_available

    @property
    def name(self):
        """Gets the name of this V1beta2DeviceRequest.  # noqa: E501

        Name can be used to reference this request in a pod.spec.containers[].resources.claims entry and in a constraint of the claim.  References using the name in the DeviceRequest will uniquely identify a request when the Exactly field is set. When the FirstAvailable field is set, a reference to the name of the DeviceRequest will match whatever subrequest is chosen by the scheduler.  Must be a DNS label.  # noqa: E501

        :return: The name of this V1beta2DeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1beta2DeviceRequest.

        Name can be used to reference this request in a pod.spec.containers[].resources.claims entry and in a constraint of the claim.  References using the name in the DeviceRequest will uniquely identify a request when the Exactly field is set. When the FirstAvailable field is set, a reference to the name of the DeviceRequest will match whatever subrequest is chosen by the scheduler.  Must be a DNS label.  # noqa: E501

        :param name: The name of this V1beta2DeviceRequest.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and name is None:  # noqa: E501
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta2DeviceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta2DeviceRequest):
            return True

        return self.to_dict() != other.to_dict()
