#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import shutil
import sys
from vanna_sys import MyVanna
from vanna_config import LLM_CONFIG, DB_CONFIG
from training_data_config import TRAINING_CONFIG

def clean_existing_data():
    """清理现有的ChromaDB数据"""
    print("🧹 清理现有数据...")
    clean_dirs = TRAINING_CONFIG.get('clean_dirs', ['./chroma', './chromadb', './data', './vanna_data', './chroma_db'])
    
    for dir_path in clean_dirs:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"   ✅ 已删除 {dir_path}")
            except Exception as e:
                print(f"   ⚠️ 删除 {dir_path} 失败: {e}")

def train_data():
    """通用训练数据函数 - 从配置文件读取"""
    project_name = TRAINING_CONFIG.get('project_name', '未知项目')
    description = TRAINING_CONFIG.get('description', '无描述')
    training_data = TRAINING_CONFIG.get('training_data', [])
    
    print(f"\n==== Vanna 训练数据 - {project_name} ====")
    print(f"📝 项目描述: {description}")
    
    # 1. 清理现有数据
    clean_existing_data()
    
    # 2. 创建Vanna实例
    print("\n🔧 初始化 Vanna...")
    vn = MyVanna(config=LLM_CONFIG)
    vn.connect_to_mysql(**DB_CONFIG)
    print("✅ Vanna初始化完成")
    
    # 3. 检查训练数据
    if not training_data:
        print("❌ 配置文件中没有找到训练数据!")
        return
    
    print(f"\n📊 开始训练 {len(training_data)} 条数据...")
    
    # 按类别统计
    categories = {}
    for item in training_data:
        category = item.get('category', '未分类')
        categories[category] = categories.get(category, 0) + 1
    
    print("📋 训练数据分类统计:")
    for category, count in categories.items():
        print(f"   {category}: {count} 条")
    
    # 4. 训练数据
    success_count = 0
    for i, item in enumerate(training_data, 1):
        question = item.get('question', '')
        sql = item.get('sql', '')
        category = item.get('category', '未分类')
        
        if not question or not sql:
            print(f"\n❌ 第 {i} 条数据格式错误，跳过")
            continue
            
        print(f"\n训练 {i}/{len(training_data)} [{category}]: {question}")
        
        try:
            result_id = vn.add_question_sql(question, sql)
            print(f"✅ 成功，ID: {result_id}")
            success_count += 1
        except Exception as e:
            print(f"❌ 失败: {e}")
    
    print(f"\n📊 训练完成统计: 成功 {success_count}/{len(training_data)} 条")
    
    # 5. 验证训练结果
    validate_training_results(vn)
    
    print(f"\n✅ {project_name}训练完成！现在可以运行: python vanna_sys.py")

def validate_training_results(vn):
    """验证训练结果"""
    print(f"\n🔍 验证训练结果...")
    
    validation_config = TRAINING_CONFIG.get('validation_config', {})
    test_questions = TRAINING_CONFIG.get('test_questions', [])
    similarity_threshold = validation_config.get('similarity_threshold', 0.8)
    
    try:
        # 检查存储的数据量
        if hasattr(vn, 'sql_collection') and vn.sql_collection:
            all_data = vn.sql_collection.get()
            docs = all_data.get('documents', [])
            print(f"总共存储了 {len(docs)} 条训练数据")
            
            if not test_questions:
                print("⚠️ 配置文件中没有测试问题")
                return
            
            # 测试关键问题
            print(f"\n🧪 测试 {len(test_questions)} 个关键问题:")
            
            for test_question in test_questions:
                print(f"\n测试问题: {test_question}")
                
                try:
                    similar_questions = vn.get_similar_question_sql(test_question, n_results=1)
                    
                    if similar_questions:
                        best_match = similar_questions[0]
                        match_question = best_match.get('question', '')
                        similarity_score = best_match.get('similarity_score', 0)
                        distance = best_match.get('distance', 'N/A')
                        
                        print(f"   匹配到: {match_question}")
                        print(f"   相似度: {similarity_score:.6f}")
                        print(f"   距离: {distance}")
                        
                        # 评估匹配质量
                        if validation_config.get('test_perfect_match', True) and match_question == test_question:
                            print(f"   🎉 完美匹配!")
                        elif validation_config.get('test_similarity', True) and similarity_score > similarity_threshold:
                            print(f"   ✅ 高度相似 (>{similarity_threshold})")
                        else:
                            print(f"   ⚠️ 相似度较低 (<={similarity_threshold})")
                    else:
                        print(f"   ❌ 没有找到匹配结果")
                        
                except Exception as e:
                    print(f"   ❌ 测试失败: {e}")
        else:
            print("❌ 无法访问训练数据集合")
        
    except Exception as e:
        print(f"验证失败: {e}")

def show_training_stats():
    """显示训练数据统计信息"""
    training_data = TRAINING_CONFIG.get('training_data', [])
    project_name = TRAINING_CONFIG.get('project_name', '未知项目')
    
    print(f"\n📊 {project_name} 训练数据统计:")
    print(f"总数据量: {len(training_data)} 条")
    
    # 按类别统计
    categories = {}
    for item in training_data:
        category = item.get('category', '未分类')
        categories[category] = categories.get(category, 0) + 1
    
    print("\n分类统计:")
    for category, count in categories.items():
        print(f"  - {category}: {count} 条")
    
    # 显示问题列表
    print("\n问题列表:")
    for i, item in enumerate(training_data, 1):
        question = item.get('question', '无问题')
        category = item.get('category', '未分类')
        print(f"  {i}. [{category}] {question}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--stats':
        # 显示统计信息
        show_training_stats()
    else:
        # 执行训练
        train_data()