# bge_config.py - BGE远程服务配置文件

# BGE远程服务配置
BGE_CONFIG = {
    # 远程BGE服务地址 - OpenAI格式
    'api_url': 'http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings',  # OpenAI兼容的embeddings端点
    
    # API认证（OpenAI格式需要）
    'api_key': 'sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ',  
    
    # 请求超时时间（秒）
    'timeout': 30,
    
    # 是否启用远程BGE服务
    'enabled': True,
    
    # 重试配置
    'max_retries': 3,
    'retry_delay': 1,  # 秒
    
    # BGE模型配置 - OpenAI格式
    'model_name': 'bge-large-zh-v1.5',  # OpenAI格式的模型名称
    'batch_size': 32  # 批量处理文本数量
}

# 备用配置 - 如果主服务不可用
BGE_FALLBACK_CONFIG = {
    'api_url': 'http://10.12.22.21:9998/v1/embeddings',  # 备用服务器
    'api_key': None,
    'timeout': 15
}

# 使用说明
USAGE_INSTRUCTIONS = """
🔧 BGE远程服务配置说明:

1. 修改 BGE_CONFIG 中的 'api_url' 为你的BGE服务地址
2. 如果你的BGE服务需要认证，设置 'api_key'
3. 根据网络情况调整 'timeout' 超时时间

📝 你的BGE服务API应该接受OpenAI格式的请求:
POST /v1/embeddings
{
    "model": "bge-large-zh-v1.5",
    "input": ["文本1", "文本2"]
}

响应格式应该是OpenAI标准格式:
{
    "data": [
        {"embedding": [向量1]},
        {"embedding": [向量2]}
    ]
}

🚀 启动你的BGE服务示例:
确保你的服务提供OpenAI兼容的/v1/embeddings端点。
"""

def validate_config():
    """验证BGE配置是否正确"""
    if not BGE_CONFIG.get('enabled', False):
        print("ℹ️ BGE远程服务已禁用")
        return False
        
    if not BGE_CONFIG.get('api_url'):
        print("❌ BGE配置错误: 缺少api_url")
        return False
        
    if not BGE_CONFIG['api_url'].startswith(('http://', 'https://')):
        print("❌ BGE配置错误: api_url必须以http://或https://开头")
        return False
        
    return True

if __name__ == "__main__":
    print(USAGE_INSTRUCTIONS)
    print(f"\n当前配置:")
    print(f"API地址: {BGE_CONFIG['api_url']}")
    print(f"启用状态: {BGE_CONFIG['enabled']}")
    print(f"超时时间: {BGE_CONFIG['timeout']}秒")
    
    if validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败，请检查配置") 
