# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1UncountedTerminatedPods(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'failed': 'list[str]',
        'succeeded': 'list[str]'
    }

    attribute_map = {
        'failed': 'failed',
        'succeeded': 'succeeded'
    }

    def __init__(self, failed=None, succeeded=None, local_vars_configuration=None):  # noqa: E501
        """V1UncountedTerminatedPods - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._failed = None
        self._succeeded = None
        self.discriminator = None

        if failed is not None:
            self.failed = failed
        if succeeded is not None:
            self.succeeded = succeeded

    @property
    def failed(self):
        """Gets the failed of this V1UncountedTerminatedPods.  # noqa: E501

        failed holds UIDs of failed Pods.  # noqa: E501

        :return: The failed of this V1UncountedTerminatedPods.  # noqa: E501
        :rtype: list[str]
        """
        return self._failed

    @failed.setter
    def failed(self, failed):
        """Sets the failed of this V1UncountedTerminatedPods.

        failed holds UIDs of failed Pods.  # noqa: E501

        :param failed: The failed of this V1UncountedTerminatedPods.  # noqa: E501
        :type: list[str]
        """

        self._failed = failed

    @property
    def succeeded(self):
        """Gets the succeeded of this V1UncountedTerminatedPods.  # noqa: E501

        succeeded holds UIDs of succeeded Pods.  # noqa: E501

        :return: The succeeded of this V1UncountedTerminatedPods.  # noqa: E501
        :rtype: list[str]
        """
        return self._succeeded

    @succeeded.setter
    def succeeded(self, succeeded):
        """Sets the succeeded of this V1UncountedTerminatedPods.

        succeeded holds UIDs of succeeded Pods.  # noqa: E501

        :param succeeded: The succeeded of this V1UncountedTerminatedPods.  # noqa: E501
        :type: list[str]
        """

        self._succeeded = succeeded

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1UncountedTerminatedPods):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1UncountedTerminatedPods):
            return True

        return self.to_dict() != other.to_dict()
