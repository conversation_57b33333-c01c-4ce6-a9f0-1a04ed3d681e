# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1beta2DeviceSubRequest(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'allocation_mode': 'str',
        'count': 'int',
        'device_class_name': 'str',
        'name': 'str',
        'selectors': 'list[V1beta2DeviceSelector]',
        'tolerations': 'list[V1beta2DeviceToleration]'
    }

    attribute_map = {
        'allocation_mode': 'allocationMode',
        'count': 'count',
        'device_class_name': 'deviceClassName',
        'name': 'name',
        'selectors': 'selectors',
        'tolerations': 'tolerations'
    }

    def __init__(self, allocation_mode=None, count=None, device_class_name=None, name=None, selectors=None, tolerations=None, local_vars_configuration=None):  # noqa: E501
        """V1beta2DeviceSubRequest - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._allocation_mode = None
        self._count = None
        self._device_class_name = None
        self._name = None
        self._selectors = None
        self._tolerations = None
        self.discriminator = None

        if allocation_mode is not None:
            self.allocation_mode = allocation_mode
        if count is not None:
            self.count = count
        self.device_class_name = device_class_name
        self.name = name
        if selectors is not None:
            self.selectors = selectors
        if tolerations is not None:
            self.tolerations = tolerations

    @property
    def allocation_mode(self):
        """Gets the allocation_mode of this V1beta2DeviceSubRequest.  # noqa: E501

        AllocationMode and its related fields define how devices are allocated to satisfy this subrequest. Supported values are:  - ExactCount: This request is for a specific number of devices.   This is the default. The exact number is provided in the   count field.  - All: This subrequest is for all of the matching devices in a pool.   Allocation will fail if some devices are already allocated,   unless adminAccess is requested.  If AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other subrequests must specify this field.  More modes may get added in the future. Clients must refuse to handle requests with unknown modes.  # noqa: E501

        :return: The allocation_mode of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: str
        """
        return self._allocation_mode

    @allocation_mode.setter
    def allocation_mode(self, allocation_mode):
        """Sets the allocation_mode of this V1beta2DeviceSubRequest.

        AllocationMode and its related fields define how devices are allocated to satisfy this subrequest. Supported values are:  - ExactCount: This request is for a specific number of devices.   This is the default. The exact number is provided in the   count field.  - All: This subrequest is for all of the matching devices in a pool.   Allocation will fail if some devices are already allocated,   unless adminAccess is requested.  If AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other subrequests must specify this field.  More modes may get added in the future. Clients must refuse to handle requests with unknown modes.  # noqa: E501

        :param allocation_mode: The allocation_mode of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: str
        """

        self._allocation_mode = allocation_mode

    @property
    def count(self):
        """Gets the count of this V1beta2DeviceSubRequest.  # noqa: E501

        Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.  # noqa: E501

        :return: The count of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this V1beta2DeviceSubRequest.

        Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.  # noqa: E501

        :param count: The count of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def device_class_name(self):
        """Gets the device_class_name of this V1beta2DeviceSubRequest.  # noqa: E501

        DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this subrequest.  A class is required. Which classes are available depends on the cluster.  Administrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.  # noqa: E501

        :return: The device_class_name of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_class_name

    @device_class_name.setter
    def device_class_name(self, device_class_name):
        """Sets the device_class_name of this V1beta2DeviceSubRequest.

        DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this subrequest.  A class is required. Which classes are available depends on the cluster.  Administrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.  # noqa: E501

        :param device_class_name: The device_class_name of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and device_class_name is None:  # noqa: E501
            raise ValueError("Invalid value for `device_class_name`, must not be `None`")  # noqa: E501

        self._device_class_name = device_class_name

    @property
    def name(self):
        """Gets the name of this V1beta2DeviceSubRequest.  # noqa: E501

        Name can be used to reference this subrequest in the list of constraints or the list of configurations for the claim. References must use the format <main request>/<subrequest>.  Must be a DNS label.  # noqa: E501

        :return: The name of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1beta2DeviceSubRequest.

        Name can be used to reference this subrequest in the list of constraints or the list of configurations for the claim. References must use the format <main request>/<subrequest>.  Must be a DNS label.  # noqa: E501

        :param name: The name of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and name is None:  # noqa: E501
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def selectors(self):
        """Gets the selectors of this V1beta2DeviceSubRequest.  # noqa: E501

        Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this subrequest. All selectors must be satisfied for a device to be considered.  # noqa: E501

        :return: The selectors of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: list[V1beta2DeviceSelector]
        """
        return self._selectors

    @selectors.setter
    def selectors(self, selectors):
        """Sets the selectors of this V1beta2DeviceSubRequest.

        Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this subrequest. All selectors must be satisfied for a device to be considered.  # noqa: E501

        :param selectors: The selectors of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: list[V1beta2DeviceSelector]
        """

        self._selectors = selectors

    @property
    def tolerations(self):
        """Gets the tolerations of this V1beta2DeviceSubRequest.  # noqa: E501

        If specified, the request's tolerations.  Tolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.  In addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.  The maximum number of tolerations is 16.  This is an alpha field and requires enabling the DRADeviceTaints feature gate.  # noqa: E501

        :return: The tolerations of this V1beta2DeviceSubRequest.  # noqa: E501
        :rtype: list[V1beta2DeviceToleration]
        """
        return self._tolerations

    @tolerations.setter
    def tolerations(self, tolerations):
        """Sets the tolerations of this V1beta2DeviceSubRequest.

        If specified, the request's tolerations.  Tolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.  In addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.  The maximum number of tolerations is 16.  This is an alpha field and requires enabling the DRADeviceTaints feature gate.  # noqa: E501

        :param tolerations: The tolerations of this V1beta2DeviceSubRequest.  # noqa: E501
        :type: list[V1beta2DeviceToleration]
        """

        self._tolerations = tolerations

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta2DeviceSubRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta2DeviceSubRequest):
            return True

        return self.to_dict() != other.to_dict()
