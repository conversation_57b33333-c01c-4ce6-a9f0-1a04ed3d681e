# vanna_config.py

# 数据库配置
DB_CONFIG = {
    'host': '**********',
    'port': 4000,
    'dbname': 'analysis_qg',
    'user': 'root',
    'password': 'ncms@Abb.235'
}

# 大模型配置
LLM_CONFIG = {
    'api_key': 'sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ',
    'model_url': 'http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions',
    'model_name': 'XiYanSQL-QwenCoder-2504',
    'temperature': 0.3
}

# Vanna AI 查询配置
VANNA_QUERY_CONFIG = {
    'return_similarity_scores': True,  # 确保返回相似度分数
    'similarity_threshold': 0.95,      # 🔧 提高阈值，只有极高相似度才匹配
    'distance_metric': 'cosine',       # 显式指定距离度量方式
    'n_results': 10,                   # 增加返回结果数量以提供更多候选
    'where': None,                     # 不使用额外的过滤条件
    'where_document': None,            # 不使用文档过滤条件
    'enable_date_sensitivity': True,   # 🔧 启用日期敏感性检查
    'date_similarity_penalty': 0.3     # 🔧 日期不匹配时的相似度惩罚
}

# 应用配置
APP_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': True,
    'allow_llm_to_see_data': True,
    'secret_key': 'dj2n3k4@#$MKF9834jk2@#$92k3j4n23k4n@#$@#$KNLKnl23423'
}

# 认证配置
AUTH_CONFIG = {
    'api_key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1',
    'admin_username': 'admin',
    'admin_password': 'Fxjt@.5000'
}