# Vanna AI Text2SQL 项目

这是一个基于 Vanna AI 的 Text2SQL 系统，专门用于分析租费数据。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

修改以下配置文件中的参数：
- `vanna_config.py` - 数据库和模型配置
- `bge_config.py` - BGE embedding 模型配置
- `training_data_config.py` - 训练数据配置

### 3. 训练数据

```bash
# 执行训练
python vanna_train.py

# 查看训练数据统计
python vanna_train.py --stats
```

### 4. 启动服务

```bash
python vanna_sys.py
```

访问 http://localhost:5000 使用系统

## 📁 项目结构

```
├── vanna_sys.py           # 主服务文件
├── vanna_config.py        # 数据库和模型配置
├── bge_config.py          # BGE模型配置  
├── vanna_train.py         # 通用训练脚本
├── training_data_config.py # 训练数据配置文件
├── requirements.txt       # 依赖管理
└── README.md              # 项目说明
```

## 🔧 配置说明

### 数据库配置
在 `vanna_config.py` 中配置您的数据库连接信息。

### BGE模型配置
在 `bge_config.py` 中配置远程BGE embedding服务地址。

### 训练数据配置 
在 `training_data_config.py` 中配置您的训练数据：
- **project_name**: 项目名称
- **description**: 项目描述  
- **training_data**: 问题和SQL对的列表
- **test_questions**: 用于验证的测试问题
- **validation_config**: 验证相关配置

训练数据格式：
```python
{
    "question": "问题描述",
    "sql": "对应的SQL语句", 
    "category": "问题分类"
}
```

## 📊 训练数据管理

### 查看训练统计
```bash
python vanna_train.py --stats
```

### 自定义训练数据
1. 编辑 `training_data_config.py`
2. 修改 `TRAINING_CONFIG` 中的训练数据
3. 运行 `python vanna_train.py` 重新训练

### 切换项目
只需要：
1. 复制项目文件夹
2. 修改 `training_data_config.py` 中的训练数据
3. 修改 `vanna_config.py` 中的数据库配置
4. 重新训练即可

## 📋 当前训练数据

项目专注于 `analysis_reference_rent` 表的租费分析，包含以下功能：
- 单站租赁费查询
- 租费趋势分析
- 省份排名
- 年度汇总

## 🔐 认证

系统支持双重认证：
- API Key 认证（用于API调用）
- 用户名密码认证（用于Web界面）

## ⚠️ 注意事项

1. 请确保BGE服务正常运行
2. 数据库连接信息正确
3. 相关API服务可访问
4. 修改训练数据后需要重新训练

## 🔄 项目复用指南

要在新项目中使用此框架：

1. **复制核心文件**：
   - `vanna_sys.py` (主系统，一般不需要修改)
   - `vanna_train.py` (训练脚本，一般不需要修改)
   
2. **修改配置文件**：
   - `vanna_config.py` - 修改数据库连接
   - `bge_config.py` - 修改BGE服务地址(如需要)
   - `training_data_config.py` - **重点修改训练数据**
   
3. **使用项目模板** (推荐):
   ```bash
   # 编辑 training_data_config.py 修改为你的数据
   ```
   
4. **重新训练**：
   ```bash
   python vanna_train.py
   ```

5. **启动服务**：
   ```bash 
   python vanna_sys.py
   ```

这样设计的好处是核心代码保持不变，只需要修改配置文件就能适配新项目！ 