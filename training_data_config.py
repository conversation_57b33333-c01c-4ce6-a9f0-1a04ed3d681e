# training_data_config.py - 训练数据配置文件

# 训练数据配置
TRAINING_CONFIG = {
    # 项目基本信息
    "project_name": "租费模块",
    "description": "analysis_reference_rent表的租费分析",
    
    # 清理配置
    "clean_dirs": ['./chroma', './chromadb', './data', './vanna_data', './chroma_db'],
    
    # 训练数据 - 只保留一个示例，更多训练通过Web界面完成
    "training_data": [
        {
            "question": "2025年4月单站租赁费是多少",
            "sql": """SELECT
    prv_id,
    prv_name,
    2025 as '年',
    4 as '月',
    sum( accountrent_contamount )/ sum( site_num )/DAY(LAST_DAY(STR_TO_DATE('2025-4-01','%Y-%m-%d')))*30.416667 AS 'value',
    '租赁总费用/租赁站址数量/当月天数*30.41667' as '计算逻辑',
    '元/站/月' as '单位'
FROM
    analysis_reference_rent
WHERE
    on_year = 2025
    AND on_month = 4
    AND rpt_type = 2
    AND is_year_to_month= 0
GROUP BY
    prv_id""",
            "category": "单站费用查询"
        }
    ],
    
    # 测试问题配置
    "test_questions": [
        "2025年4月单站租赁费是多少"
    ],
    
    # UI示例问题配置 - 用于启动时的测试和用户界面示例
    "ui_sample_questions": [
        "2025年4月单站租赁费是多少",
        "租赁费用月度趋势分析",
        "单站租赁费最高的省份排名"
    ],
    
    # 启动时测试问题配置
    "startup_test_question": "2025年4月单站租赁费是多少",
    
    # 验证配置
    "validation_config": {
        "similarity_threshold": 0.8,  # 相似度阈值
        "test_perfect_match": True,   # 是否测试完美匹配
        "test_similarity": True       # 是否测试相似度
    }
} 