# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1StatefulSetPersistentVolumeClaimRetentionPolicy(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'when_deleted': 'str',
        'when_scaled': 'str'
    }

    attribute_map = {
        'when_deleted': 'whenDeleted',
        'when_scaled': 'whenScaled'
    }

    def __init__(self, when_deleted=None, when_scaled=None, local_vars_configuration=None):  # noqa: E501
        """V1StatefulSetPersistentVolumeClaimRetentionPolicy - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._when_deleted = None
        self._when_scaled = None
        self.discriminator = None

        if when_deleted is not None:
            self.when_deleted = when_deleted
        if when_scaled is not None:
            self.when_scaled = when_scaled

    @property
    def when_deleted(self):
        """Gets the when_deleted of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501

        WhenDeleted specifies what happens to PVCs created from StatefulSet VolumeClaimTemplates when the StatefulSet is deleted. The default policy of `Retain` causes PVCs to not be affected by StatefulSet deletion. The `Delete` policy causes those PVCs to be deleted.  # noqa: E501

        :return: The when_deleted of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501
        :rtype: str
        """
        return self._when_deleted

    @when_deleted.setter
    def when_deleted(self, when_deleted):
        """Sets the when_deleted of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.

        WhenDeleted specifies what happens to PVCs created from StatefulSet VolumeClaimTemplates when the StatefulSet is deleted. The default policy of `Retain` causes PVCs to not be affected by StatefulSet deletion. The `Delete` policy causes those PVCs to be deleted.  # noqa: E501

        :param when_deleted: The when_deleted of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501
        :type: str
        """

        self._when_deleted = when_deleted

    @property
    def when_scaled(self):
        """Gets the when_scaled of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501

        WhenScaled specifies what happens to PVCs created from StatefulSet VolumeClaimTemplates when the StatefulSet is scaled down. The default policy of `Retain` causes PVCs to not be affected by a scaledown. The `Delete` policy causes the associated PVCs for any excess pods above the replica count to be deleted.  # noqa: E501

        :return: The when_scaled of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501
        :rtype: str
        """
        return self._when_scaled

    @when_scaled.setter
    def when_scaled(self, when_scaled):
        """Sets the when_scaled of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.

        WhenScaled specifies what happens to PVCs created from StatefulSet VolumeClaimTemplates when the StatefulSet is scaled down. The default policy of `Retain` causes PVCs to not be affected by a scaledown. The `Delete` policy causes the associated PVCs for any excess pods above the replica count to be deleted.  # noqa: E501

        :param when_scaled: The when_scaled of this V1StatefulSetPersistentVolumeClaimRetentionPolicy.  # noqa: E501
        :type: str
        """

        self._when_scaled = when_scaled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1StatefulSetPersistentVolumeClaimRetentionPolicy):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1StatefulSetPersistentVolumeClaimRetentionPolicy):
            return True

        return self.to_dict() != other.to_dict()
