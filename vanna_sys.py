# vanna_sys.py - 只负责启动服务，不包含训练代码
from vanna.base import VannaBase
from vanna.chromadb import ChromaDB_VectorStore
from vanna.flask import VannaFlaskApp
import requests
import json
import pandas as pd
import time
import re
from flask import Flask, request, jsonify, session, redirect, url_for
from functools import wraps
from abc import ABC, abstractmethod
import numpy as np
import uuid
import hashlib
from datetime import datetime

# 导入配置和辅助函数
from vanna_config import (
    DB_CONFIG, LLM_CONFIG, APP_CONFIG, AUTH_CONFIG, VANNA_QUERY_CONFIG,
    BGE_CONFIG, BGE_FALLBACK_CONFIG, validate_bge_config, DATA_CONFIG
)
from training_data_config import TRAINING_CONFIG

# 自定义远程BGE Embedding函数
class RemoteBGEEmbeddingFunction:
    """通过HTTP API调用远程BGE模型的embedding函数"""
    
    def __init__(self, api_url, api_key=None, timeout=30):
        """
        初始化远程BGE embedding函数
        
        Args:
            api_url: 远程BGE服务的API地址
            api_key: API密钥（如果需要）
            timeout: 请求超时时间
        """
        self.api_url = api_url
        self.api_key = api_key
        self.timeout = timeout
        
    def __call__(self, input):
        """
        调用远程BGE模型获取embedding向量
        
        Args:
            input: 文本列表（ChromaDB 0.4.16+ 要求使用input参数）
            
        Returns:
            embedding向量列表
        """
        try:
            # 准备请求头
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # 如果有API密钥，添加到请求头
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            # 准备请求数据 - 使用Xinference/OpenAI兼容格式
            data = {
                'model': 'bge-large-zh-v1.5',  # 使用固定的模型名称
                'input': input if isinstance(input, list) else [input]  # 使用'input'字段
            }
            
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 提取embedding向量 - 兼容Xinference/OpenAI格式
            embeddings = []
            if 'data' in result:
                # OpenAI/Xinference格式: {"data": [{"embedding": [向量]}]}
                for item in result['data']:
                    if 'embedding' in item:
                        embeddings.append(item['embedding'])
            elif 'embeddings' in result:
                # 备用格式: {"embeddings": [[向量1], [向量2]]}
                embeddings = result['embeddings']
            else:
                # 如果响应格式不符合预期，尝试直接使用结果
                embeddings = result
            
            # 确保返回的是numpy数组列表
            if isinstance(embeddings, list) and len(embeddings) > 0:
                return [np.array(emb) for emb in embeddings]
            else:
                print(f"⚠️ 意外的BGE API响应格式: {result}")
                # 返回零向量作为备选
                return [np.zeros(1024) for _ in (input if isinstance(input, list) else [input])]
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 远程BGE API请求失败: {e}")
            # 返回零向量作为备选
            return [np.zeros(1024) for _ in (input if isinstance(input, list) else [input])]
        except Exception as e:
            print(f"❌ 远程BGE embedding处理失败: {e}")
            # 返回零向量作为备选
            return [np.zeros(1024) for _ in (input if isinstance(input, list) else [input])]

# 简单的认证接口
class SimpleAuth(ABC):
    @abstractmethod
    def login_form(self):
        pass

    @abstractmethod
    def login_handler(self, request):
        pass

    @abstractmethod
    def get_user(self, request):
        pass

    @abstractmethod
    def is_logged_in(self, user):
        pass

class HybridAuth(SimpleAuth):
    def __init__(self, api_key, admin_username, admin_password):
        self.api_key = api_key
        self.admin_username = admin_username
        self.admin_password = admin_password

    def login_form(self):
        # 终极解决方案 - 绕过VannaFlaskApp的JavaScript渲染问题
        import random
        import string
        
        # 生成服务器端验证码
        captcha_text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        
        # 使用最原始的方法，直接在window对象上定义函数
        html_template = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>登录 - 文本转SQL</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; display: flex; align-items: center; justify-content: center; }}
        .login-container {{ background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1); padding: 40px; width: 100%; max-width: 400px; box-sizing: border-box; }}
        .login-header {{ text-align: center; margin-bottom: 30px; }}
        .login-header h1 {{ color: #333; margin-bottom: 5px; font-size: 28px; }}
        .login-header p {{ color: #666; margin: 0; font-size: 14px; }}
        .form-group {{ margin-bottom: 20px; position: relative; }}
        .form-group label {{ display: block; color: #333; font-weight: 500; margin-bottom: 8px; }}
        .form-group input {{ width: 100%; padding: 12px 15px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; transition: all 0.3s ease; box-sizing: border-box; background: #fff; }}
        .form-group input:focus {{ outline: none; border-color: #667eea; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }}
        .captcha-group {{ display: flex; gap: 10px; align-items: center; }}
        .captcha-input {{ flex: 1; }}
        .captcha-display {{ background: #f8f9fa; border: 2px solid #e1e5e9; border-radius: 8px; padding: 12px 15px; font-size: 18px; font-weight: bold; color: #333; letter-spacing: 3px; text-align: center; min-width: 80px; font-family: monospace; cursor: pointer; transition: all 0.3s ease; user-select: none; }}
        .captcha-display:hover {{ background: #e9ecef; border-color: #667eea; transform: scale(1.05); }}
        .captcha-display:active {{ transform: scale(0.95); }}
        .refresh-link {{ color: #667eea; font-size: 12px; text-decoration: none; display: inline-block; margin-top: 5px; cursor: pointer; }}
        .refresh-link:hover {{ text-decoration: underline; }}
        .login-btn {{ width: 100%; padding: 14px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; margin-top: 10px; box-sizing: border-box; }}
        .login-btn:hover {{ transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); }}
        .login-btn:active {{ transform: translateY(0); }}
        .error-message {{ color: #e74c3c; font-size: 14px; margin-top: 10px; text-align: center; display: none; }}
        .status-info {{ background: #e8f5e8; color: #2e7d32; padding: 8px; margin-top: 10px; border-radius: 5px; font-size: 12px; text-align: center; }}
        @media (max-width: 480px) {{ .login-container {{ margin: 20px; padding: 30px 20px; }} }}
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>文本转SQL训练</h1>
            <p>请输入您的登录凭据</p>
        </div>
        
        <form method="POST" action="/auth/login" onsubmit="return window.validateCaptcha()">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <div class="form-group">
                <label for="captcha">验证码</label>
                <div class="captcha-group">
                    <input type="text" id="captcha" name="captcha" class="captcha-input" placeholder="请输入验证码" required maxlength="4" oninput="window.hideCaptchaError()">
                    <div class="captcha-display" id="captchaDisplay" onclick="window.refreshCaptcha()" title="点击刷新验证码">{captcha_text}</div>
                </div>
                <a href="javascript:void(0)" onclick="window.refreshCaptcha()" class="refresh-link">看不清？点击刷新</a>
            </div>
            
            <input type="hidden" name="captcha_answer" id="captchaAnswer" value="{captcha_text}">
            
            <button type="submit" class="login-btn">登录</button>
            
            <div class="error-message" id="errorMessage"></div>
        </form>
    </div>

    <script>
        // 立即执行，直接绑定到window对象
        (function() {{
            console.log('🚀 开始初始化验证码系统...');
            
            // 生成验证码函数
            window.generateCaptcha = function() {{
                var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                var result = '';
                for (var i = 0; i < 4; i++) {{
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }}
                return result;
            }};
            
            // 刷新验证码函数
            window.refreshCaptcha = function() {{
                console.log('✅ refreshCaptcha被调用');
                try {{
                    var captchaDisplay = document.getElementById('captchaDisplay');
                    var captchaAnswer = document.getElementById('captchaAnswer');
                    var captchaInput = document.getElementById('captcha');
                    
                    if (!captchaDisplay) {{
                        console.error('找不到验证码显示元素');
                        return;
                    }}
                    
                    if (!captchaAnswer) {{
                        console.error('找不到验证码答案元素');
                        return;
                    }}
                    
                    var newCaptcha = window.generateCaptcha();
                    captchaDisplay.innerHTML = newCaptcha;
                    captchaAnswer.value = newCaptcha;
                    
                    if (captchaInput) {{
                        captchaInput.value = '';
                        captchaInput.focus();
                    }}
                    
                    var errorMsg = document.getElementById('errorMessage');
                    if (errorMsg) {{
                        errorMsg.style.display = 'none';
                    }}
                    
                    console.log('验证码已更新为：' + newCaptcha);
                }} catch (e) {{
                    console.error('刷新验证码出错：', e);
                }}
            }};
            
            // 验证表单函数
            window.validateCaptcha = function() {{
                try {{
                    var captchaInput = document.getElementById('captcha').value.toUpperCase();
                    var captchaAnswer = document.getElementById('captchaAnswer').value.toUpperCase();
                    var errorMsg = document.getElementById('errorMessage');
                    
                    console.log('验证验证码...');
                    console.log('输入：' + captchaInput);
                    console.log('答案：' + captchaAnswer);
                    
                    if (!captchaAnswer || captchaInput !== captchaAnswer) {{
                        if (errorMsg) {{
                            errorMsg.innerHTML = '验证码错误，请重新输入';
                            errorMsg.style.display = 'block';
                        }}
                        window.refreshCaptcha();
                        return false;
                    }}
                    
                    if (errorMsg) {{
                        errorMsg.style.display = 'none';
                    }}
                    return true;
                }} catch (e) {{
                    console.error('验证出错：', e);
                    return false;
                }}
            }};
            
            // 隐藏错误函数
            window.hideCaptchaError = function() {{
                var errorMsg = document.getElementById('errorMessage');
                if (errorMsg) {{
                    errorMsg.style.display = 'none';
                }}
            }};
            
            console.log('✅ 验证码系统初始化完成');
            console.log('当前验证码：{captcha_text}');
            
            // 测试函数是否正确绑定
            setTimeout(function() {{
                if (typeof window.refreshCaptcha === 'function') {{
                    console.log('✅ window.refreshCaptcha 可用');
                }} else {{
                    console.error('❌ window.refreshCaptcha 不可用');
                }}
                
                if (typeof window.validateCaptcha === 'function') {{
                    console.log('✅ window.validateCaptcha 可用');
                }} else {{
                    console.error('❌ window.validateCaptcha 不可用');
                }}
            }}, 100);
        }})();
    </script>
</body>
</html>'''
        
        return html_template.format(captcha_text=captcha_text)

    def login_handler(self, request):
        # 处理Web页面的登录请求，添加验证码验证
        username = request.form.get('username')
        password = request.form.get('password')
        captcha = request.form.get('captcha', '').upper()
        captcha_answer = request.form.get('captcha_answer', '').upper()
        
        # 验证码检查
        if not captcha or not captcha_answer or captcha != captcha_answer:
            return '''
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>验证码错误</title>
                <style>
                    body {
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                        margin: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .error-container {
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                        text-align: center;
                        max-width: 400px;
                    }
                    .error-icon {
                        color: #e74c3c;
                        font-size: 48px;
                        margin-bottom: 20px;
                    }
                    .error-message {
                        color: #333;
                        font-size: 18px;
                        margin-bottom: 20px;
                    }
                    .back-btn {
                        background: #667eea;
                        color: white;
                        padding: 12px 24px;
                        border: none;
                        border-radius: 8px;
                        text-decoration: none;
                        display: inline-block;
                        transition: background 0.3s;
                    }
                    .back-btn:hover {
                        background: #5a67d8;
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-icon">🔐</div>
                    <div class="error-message">验证码错误，请重新输入</div>
                    <a href="/auth/login" class="back-btn">返回登录</a>
                </div>
                <script>
                    setTimeout(function() {{
                        window.location.href = '/auth/login';
                    }}, 2000);
                </script>
            </body>
            </html>
            ''', 400
        
        # 用户名密码检查
        if username == self.admin_username and password == self.admin_password:
            session['user'] = {'username': username, 'role': 'admin'}
            return redirect('/')
        
        # 登录失败，返回错误页面
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>登录失败</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error-container {
                    background: white;
                    padding: 40px;
                    border-radius: 15px;
                    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                    text-align: center;
                    max-width: 400px;
                }
                .error-icon {
                    color: #e74c3c;
                    font-size: 48px;
                    margin-bottom: 20px;
                }
                .error-message {
                    color: #333;
                    font-size: 18px;
                    margin-bottom: 20px;
                }
                .back-btn {
                    background: #667eea;
                    color: white;
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    text-decoration: none;
                    display: inline-block;
                    transition: background 0.3s;
                }
                .back-btn:hover {
                    background: #5a67d8;
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-message">用户名或密码错误</div>
                <a href="/auth/login" class="back-btn">返回登录</a>
            </div>
            <script>
                setTimeout(function() {{
                    window.location.href = '/auth/login';
                }}, 3000);
            </script>
        </body>
        </html>
        ''', 401

    def get_user(self, request):
        # 首先检查API Key认证
        api_key = request.headers.get('X-API-Key')
        if api_key and api_key == self.api_key:
            return {'username': 'api_user', 'role': 'admin'}
        
        # 如果不是API Key认证，则检查session认证
        return session.get('user')

    def is_logged_in(self, user):
        return user is not None

    def logout_handler(self, request):
        session.clear()
        return redirect('/auth/login')

    def callback_handler(self, request):
        return '', 404

    def override_config_for_user(self, user, config):
        return config

class MyCustomLLM(VannaBase):
    def __init__(self, config=None):
        if config is None:
            raise ValueError(
                "配置必须提供 api_key、model_url 和 model_name"
            )

        if "api_key" not in config:
            raise ValueError("配置必须包含 api_key")

        if "model_url" not in config:
            raise ValueError("配置必须包含 model_url")

        if "model_name" not in config:
            raise ValueError("配置必须包含 model_name")

        self.api_key = config["api_key"]
        self.model_url = config["model_url"]
        self.model_name = config["model_name"]
        # 添加默认温度参数
        self.temperature = config.get("temperature", 0.7)

    def system_message(self, message: str) -> any:
        return {"role": "system", "content": message}

    def user_message(self, message: str) -> any:
        return {"role": "user", "content": message}

    def assistant_message(self, message: str) -> any:
        return {"role": "assistant", "content": message}

    def generate_sql(self, question: str, **kwargs) -> str:
        """
        重写以添加调试信息和更宽松的匹配逻辑，同时应用相似度配置
        """
        # 合并全局配置和传入的参数
        config = {**VANNA_QUERY_CONFIG, **kwargs}
        
        # 强制输出日志到控制台
        import sys
        print(f"\n=== 🚀 开始处理问题: {question} ===", flush=True)
        print(f"📊 使用相似度阈值: {config.get('similarity_threshold', VANNA_QUERY_CONFIG.get('similarity_threshold', 0.7))}", flush=True)
        print(f"📊 返回相似度分数: {config.get('return_similarity_scores', VANNA_QUERY_CONFIG.get('return_similarity_scores', True))}", flush=True)
        sys.stdout.flush()
        
        try:
            # 获取相似的问题-SQL对，应用配置
            print("🔍 开始向量检索...", flush=True)
            similar_question_sql_pairs = self.get_similar_question_sql(question, **config)
            
            if similar_question_sql_pairs and len(similar_question_sql_pairs) > 0:
                print(f"✅ 找到 {len(similar_question_sql_pairs)} 个相似问题:", flush=True)
                
                for i, pair in enumerate(similar_question_sql_pairs[:3]):  # 只显示前3个
                    print(f"  {i+1}. 问题: {pair.get('question', 'N/A')}", flush=True)
                    
                    # 根据配置决定是否显示相似度分数
                    if config.get('return_similarity_scores', VANNA_QUERY_CONFIG.get('return_similarity_scores', True)):
                        similarity_score = pair.get('similarity_score')
                        distance = pair.get('distance', 'N/A')
                        if similarity_score is not None:
                            print(f"     相似度分数: {similarity_score:.4f}", flush=True)
                        else:
                            print(f"     距离: {distance}", flush=True)
                    
                    print(f"     SQL: {pair.get('sql', 'N/A')[:100]}...", flush=True)
                
                # 检查最相似的结果
                best_match = similar_question_sql_pairs[0]
                
                # 使用配置中的相似度阈值
                similarity_threshold = config.get('similarity_threshold', VANNA_QUERY_CONFIG.get('similarity_threshold', 0.7))
                
                # 获取相似度指标
                similarity_score = best_match.get('similarity_score')
                distance = best_match.get('distance', float('inf'))
                
                if similarity_score is not None:
                    # 使用相似度分数（值越高越相似）
                    print(f"📊 最佳匹配相似度分数: {similarity_score:.4f}, 阈值: {similarity_threshold}", flush=True)
                    similarity_check = similarity_score >= similarity_threshold
                else:
                    # 使用距离（值越小越相似，需要反转阈值逻辑）
                    distance_threshold = 1.0 - similarity_threshold  # 将相似度阈值转换为距离阈值
                    print(f"📊 最佳匹配距离: {distance}, 距离阈值: {distance_threshold}", flush=True)
                    similarity_check = distance <= distance_threshold
                
                # 🔧 额外的完全匹配检查
                exact_match = False
                exact_match_pair = None
                for pair in similar_question_sql_pairs:
                    if pair.get('question', '').strip().lower() == question.strip().lower():
                        print(f"🎯 发现完全匹配的问题: '{pair.get('question', '')}'", flush=True)
                        print(f"   (完全匹配优先，忽略相似度分数)", flush=True)
                        
                        # 🔬 诊断：为什么完全相同的文本相似度这么低？
                        original_question = pair.get('question', '')
                        input_question = question
                        distance = pair.get('distance', 'N/A')
                        similarity = pair.get('similarity_score', 'N/A')
                        
                        print(f"🔬 相似度诊断:")
                        print(f"   输入问题: '{input_question}'")
                        print(f"   存储问题: '{original_question}'")
                        print(f"   字符串相等: {input_question.strip().lower() == original_question.strip().lower()}")
                        print(f"   距离: {distance}")
                        print(f"   相似度: {similarity}")
                        
                        # 测试向量生成一致性
                        try:
                            if hasattr(self, 'generate_embedding'):
                                input_vector = self.generate_embedding(input_question)
                                stored_vector = self.generate_embedding(original_question)
                                
                                if input_vector is not None and stored_vector is not None:
                                    # 计算余弦相似度
                                    cos_sim = np.dot(input_vector, stored_vector) / (np.linalg.norm(input_vector) * np.linalg.norm(stored_vector))
                                    # 计算余弦距离
                                    cos_dist = 1.0 - cos_sim
                                    
                                    print(f"   🧮 重新计算向量:")
                                    print(f"     输入向量长度: {len(input_vector)}")
                                    print(f"     存储向量长度: {len(stored_vector)}")
                                    print(f"     余弦相似度: {cos_sim:.6f}")
                                    print(f"     余弦距离: {cos_dist:.6f}")
                                    print(f"     ChromaDB返回距离: {distance}")
                                    
                                    if abs(cos_dist - distance) > 0.01:
                                        print(f"     ⚠️  距离计算不一致！可能的问题:")
                                        print(f"       - 存储时和查询时的向量生成不同")
                                        print(f"       - ChromaDB内部处理差异")
                                        print(f"       - 文本预处理不一致")
                                else:
                                    print(f"   ❌ 无法生成向量进行对比")
                        except Exception as e:
                            print(f"   ❌ 向量诊断失败: {e}")
                        
                        exact_match_pair = pair
                        sql = pair.get('sql', '')
                        if sql and sql.strip():
                            print(f"✅ 返回完全匹配的SQL: {sql}", flush=True)
                            return sql.replace("\\_", "_")
                        exact_match = True
                        break
                
                if not exact_match and similarity_check:
                    sql = best_match.get('sql', '')
                    if sql and sql.strip():
                        print(f"✅ 返回相似匹配的SQL: {sql}", flush=True)
                        return sql.replace("\\_", "_")
                
                if exact_match:
                    print(f"⚠️  完全匹配但SQL为空，继续调用大模型", flush=True)
                else:
                    if similarity_score is not None:
                        print(f"❌ 相似度分数 {similarity_score:.4f} < 阈值 {similarity_threshold}, 调用大模型", flush=True)
                    else:
                        print(f"❌ 距离 {distance} > 阈值 {1.0 - similarity_threshold}, 调用大模型", flush=True)
            else:
                print("❌ 未找到任何相似问题，调用大模型", flush=True)
        
        except Exception as e:
            print(f"❌ 向量检索出错: {e}", flush=True)
            import traceback
            print(f"详细错误: {traceback.format_exc()}", flush=True)
        
        # 如果没有找到合适的匹配，调用父类的默认方法
        print("🤖 开始调用大模型生成SQL...", flush=True)
        start_time = time.time()
        
        try:
            sql = super().generate_sql(question, **config)
            end_time = time.time()
            print(f"✅ 大模型调用完成，耗时: {end_time - start_time:.2f}秒", flush=True)
            return sql.replace("\\_", "_")
        except Exception as e:
            end_time = time.time()
            print(f"❌ 大模型调用失败，耗时: {end_time - start_time:.2f}秒，错误: {e}", flush=True)
            raise

    def submit_prompt(self, prompt, **kwargs) -> str:
        if prompt is None or len(prompt) == 0:
            raise ValueError("Prompt cannot be empty")
        # 在最后一条用户消息末尾添加/no_think
        if prompt and len(prompt) > 0:
            last_message_index = -1
            # 查找最后一条用户消息
            for i in range(len(prompt) - 1, -1, -1):
                if prompt[i].get('role') == 'user':
                    last_message_index = i
                    break
               
            if last_message_index != -1:
                # 在最后一条用户消息末尾添加/no_think
                original_content = prompt[last_message_index].get('content', '')
                if not original_content.endswith('/no_think'):
                    prompt[last_message_index]['content'] = original_content + ' /no_think'

        # 准备请求头
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        
        # 准备请求体
        data = {
            'model': self.model_name,
            'stream': False,
            'messages': prompt,
            'temperature': kwargs.get('temperature', self.temperature)
        }
        
        try:
            # 发送请求
            response = requests.post(
                self.model_url,
                headers=headers,
                json=data,
                timeout=60  # 添加超时设置
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 检查响应格式
            if not result.get('choices') or len(result['choices']) == 0:
                raise ValueError("Invalid response format from API")
            
            # 获取响应内容
            content = result['choices'][0].get('message', {}).get('content')
            if content is None:
                raise ValueError("No content in API response")
                
            # 处理返回结果中的think标签
            def remove_think_tags(text):
                try:
                    # 尝试移除<think>标签
                    # 使用非贪婪模式匹配，确保正确处理嵌套标签
                    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
                    # 如果结果为空，返回原文本
                    return cleaned_text.strip() if cleaned_text.strip() else text
                except Exception:
                    # 如果处理出错，返回原文本
                    return text

            # 处理返回内容
            cleaned_content = remove_think_tags(content)
            return cleaned_content
            
        except requests.exceptions.RequestException as e:
            # 处理网络请求相关错误
            raise Exception(f"API request failed: {str(e)}")
        except json.JSONDecodeError:
            # 处理JSON解析错误
            raise Exception("Failed to parse API response")
        except Exception as e:
            # 处理其他错误
            raise Exception(f"Error in submit_prompt: {str(e)}")

class MyVanna(ChromaDB_VectorStore, MyCustomLLM):
    def __init__(self, config=None):
        # 配置远程BGE embedding模型
        if BGE_CONFIG.get('enabled', False):
            try:
                print(f"🚀 正在配置远程BGE embedding服务: {BGE_CONFIG['api_url']}")
                
                # 创建远程BGE embedding函数
                remote_bge_function = RemoteBGEEmbeddingFunction(
                    api_url=BGE_CONFIG['api_url'],
                    api_key=BGE_CONFIG.get('api_key'),
                    timeout=BGE_CONFIG.get('timeout', 30)
                )
                
                # 测试远程服务是否可用
                try:
                    print("🔍 测试远程BGE服务连接...")
                    test_result = remote_bge_function(["测试连接"])
                    if test_result and len(test_result) > 0 and len(test_result[0]) > 0:
                        print("✅ 远程BGE服务连接成功!")
                        print(f"   向量维度: {len(test_result[0])}")
                        
                        # 如果没有提供config，创建一个
                        if config is None:
                            config = {}
                        
                        # 添加embedding函数到配置中
                        config['embedding_function'] = remote_bge_function
                    else:
                        print("⚠️ 远程BGE服务测试失败，使用默认embedding模型")
                        
                except Exception as test_e:
                    print(f"⚠️ 远程BGE服务测试连接失败: {test_e}")
                    
                    # 尝试备用服务器
                    if BGE_FALLBACK_CONFIG.get('api_url'):
                        print(f"🔄 尝试备用BGE服务: {BGE_FALLBACK_CONFIG['api_url']}")
                        try:
                            fallback_function = RemoteBGEEmbeddingFunction(
                                api_url=BGE_FALLBACK_CONFIG['api_url'],
                                api_key=BGE_FALLBACK_CONFIG.get('api_key'),
                                timeout=BGE_FALLBACK_CONFIG.get('timeout', 15)
                            )
                            
                            test_result = fallback_function(["测试连接"])
                            if test_result and len(test_result) > 0 and len(test_result[0]) > 0:
                                print("✅ 备用BGE服务连接成功!")
                                if config is None:
                                    config = {}
                                config['embedding_function'] = fallback_function
                            else:
                                print("⚠️ 备用BGE服务也无法连接")
                        except Exception as fallback_e:
                            print(f"⚠️ 备用BGE服务连接失败: {fallback_e}")
                    
                    print("💡 请检查:")
                    print(f"   1. 服务器地址是否正确: {BGE_CONFIG['api_url']}")
                    print("   2. 服务器是否运行BGE embedding服务")
                    print("   3. 网络连接是否正常")
                    print("   4. 如需API密钥，请在bge_config.py中配置")
                    print("   ⏳ 将使用默认embedding模型继续运行")
                
            except Exception as e:
                print(f"⚠️ 配置远程BGE embedding失败: {e}")
                print("   将使用默认embedding模型")
        else:
            print("ℹ️ 远程BGE服务已禁用，使用默认embedding模型")
        
        # 确保数据目录存在
        import os
        os.makedirs(DATA_CONFIG['data_dir'], exist_ok=True)
        os.makedirs(DATA_CONFIG['chromadb_path'], exist_ok=True)

        # 为ChromaDB配置添加路径
        if config is None:
            config = {}

        # 添加ChromaDB存储路径配置
        config['path'] = DATA_CONFIG['chromadb_path']

        # 初始化父类
        ChromaDB_VectorStore.__init__(self, config=config)
        MyCustomLLM.__init__(self, config=config)
        
    def get_similar_question_sql(self, question: str, **kwargs) -> list:
        """
        重写get_similar_question_sql方法，正确处理距离和相似度信息，并添加日期敏感性检查
        """
        try:
            # 获取配置参数，优先从VANNA_QUERY_CONFIG读取默认值
            n_results = kwargs.get('n_results', VANNA_QUERY_CONFIG.get('n_results', 10))
            return_similarity_scores = kwargs.get('return_similarity_scores', VANNA_QUERY_CONFIG.get('return_similarity_scores', True))
            enable_date_sensitivity = kwargs.get('enable_date_sensitivity', VANNA_QUERY_CONFIG.get('enable_date_sensitivity', False))
            date_penalty = kwargs.get('date_similarity_penalty', VANNA_QUERY_CONFIG.get('date_similarity_penalty', 0.3))
            
            print(f"🔍 ChromaDB查询参数: n_results={n_results}, return_similarity_scores={return_similarity_scores}", flush=True)
            if enable_date_sensitivity:
                print(f"📅 启用日期敏感性检查，惩罚系数: {date_penalty}", flush=True)
            
            # 检查集合是否存在
            if not hasattr(self, 'sql_collection') or self.sql_collection is None:
                print("❌ SQL集合不存在，尝试获取或创建", flush=True)
                # 尝试获取默认集合
                if hasattr(self, 'collection'):
                    self.sql_collection = self.collection
                else:
                    print("❌ 没有可用的ChromaDB集合", flush=True)
                    return []
            
            # 🔧 提取输入问题中的日期信息
            input_dates = self._extract_dates_from_text(question) if enable_date_sensitivity else []
            if input_dates:
                print(f"📅 输入问题中检测到日期: {input_dates}", flush=True)
            
            # 执行ChromaDB查询
            print(f"🔍 执行ChromaDB查询，问题: '{question}'", flush=True)
            query_results = self.sql_collection.query(
                query_texts=[question],
                n_results=n_results,
                include=['documents', 'distances', 'metadatas']  # 明确包含距离信息
            )
            
            print(f"📊 ChromaDB原始查询结果类型: {type(query_results)}", flush=True)
            print(f"📊 ChromaDB原始查询结果键: {list(query_results.keys()) if isinstance(query_results, dict) else 'Not dict'}", flush=True)
            
            # 处理查询结果
            if query_results is None:
                print("❌ ChromaDB查询返回None", flush=True)
                return []
            
            # 提取各个组件
            documents = query_results.get('documents', [[]])[0] if query_results.get('documents') else []
            distances = query_results.get('distances', [[]])[0] if query_results.get('distances') else []
            metadatas = query_results.get('metadatas', [[]])[0] if query_results.get('metadatas') else []
            
            print(f"📊 提取结果: documents={len(documents)}, distances={len(distances)}, metadatas={len(metadatas)}", flush=True)
            
            if len(documents) == 0:
                print("ℹ️ 没有找到任何文档", flush=True)
                return []
            
            # 构建结果列表
            results = []
            for i, doc in enumerate(documents):
                try:
                    # 解析文档JSON
                    if isinstance(doc, str):
                        doc_data = json.loads(doc)
                    else:
                        doc_data = doc
                    
                    # 获取距离信息
                    distance = distances[i] if i < len(distances) else float('inf')
                    
                    # 计算相似度分数 - 修正：ChromaDB使用欧几里得距离平方，不是余弦距离
                    if distance != float('inf') and distance is not None:
                        # ChromaDB使用欧几里得距离平方
                        # 对于欧几里得距离平方，距离越小相似度越高
                        # 使用指数衰减函数将距离转换为0-1的相似度分数
                        # 当距离为0时相似度为1，距离越大相似度越小
                        similarity_score = np.exp(-distance / 2.0)  # 调整衰减参数以获得合理的分数范围
                    else:
                        similarity_score = 0.0
                    
                    # 🔧 日期敏感性检查和惩罚
                    original_similarity = similarity_score
                    stored_question = doc_data.get('question', '')
                    
                    if enable_date_sensitivity and input_dates and stored_question:
                        stored_dates = self._extract_dates_from_text(stored_question)
                        
                        if stored_dates:
                            # 检查日期是否匹配
                            date_match = self._check_date_match(input_dates, stored_dates)
                            
                            if not date_match:
                                # 应用日期不匹配惩罚
                                similarity_score = similarity_score * (1 - date_penalty)
                                print(f"   📅 日期不匹配惩罚: '{stored_question[:50]}...'", flush=True)
                                print(f"      输入日期: {input_dates}, 存储日期: {stored_dates}", flush=True)
                                print(f"      原始相似度: {original_similarity:.4f} → 惩罚后: {similarity_score:.4f}", flush=True)
                            else:
                                print(f"   ✅ 日期匹配: '{stored_question[:50]}...'", flush=True)
                    
                    # 构建结果项
                    result_item = {
                        'question': stored_question,
                        'sql': doc_data.get('sql', ''),
                        'distance': distance,
                        'similarity_score': similarity_score,
                        'original_similarity': original_similarity  # 保存原始相似度以便调试
                    }
                    
                    # 添加元数据
                    if i < len(metadatas) and metadatas[i]:
                        result_item.update(metadatas[i])
                    
                    results.append(result_item)
                    
                    print(f"   结果{i}: 问题='{stored_question[:50]}...', 距离={distance:.4f}, 相似度={similarity_score:.4f}", flush=True)
                    
                except json.JSONDecodeError as e:
                    print(f"❌ 解析文档{i}的JSON失败: {e}", flush=True)
                    # 作为备用，直接使用原始文档
                    results.append({
                        'question': str(doc),
                        'sql': '',
                        'distance': distances[i] if i < len(distances) else float('inf'),
                        'similarity_score': 0.0
                    })
                except Exception as e:
                    print(f"❌ 处理文档{i}时出错: {e}", flush=True)
                    continue
            
            # 按相似度分数排序（相似度高的在前）
            results.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            
            print(f"✅ 成功处理 {len(results)} 个结果，按相似度排序完成", flush=True)
            
            # 🔧 输出前几个结果的详细信息以便调试
            if results and enable_date_sensitivity:
                print("🔍 排序后的前3个结果:")
                for i, result in enumerate(results[:3]):
                    print(f"   {i+1}. 问题: '{result.get('question', '')[:60]}...'")
                    print(f"      相似度: {result.get('similarity_score', 0):.4f} (原始: {result.get('original_similarity', 0):.4f})")
            
            return results
            
        except Exception as e:
            print(f"❌ get_similar_question_sql出错: {e}", flush=True)
            import traceback
            print(f"详细错误: {traceback.format_exc()}", flush=True)
            
            # 作为备用，调用原始方法
            try:
                print("🔄 尝试调用原始方法作为备用", flush=True)
                original_results = super().get_similar_question_sql(question, **kwargs)
                
                # 为原始结果添加缺失的距离信息
                if original_results:
                    for i, item in enumerate(original_results):
                        if isinstance(item, dict):
                            if 'distance' not in item:
                                item['distance'] = float('inf')
                            if 'similarity_score' not in item:
                                item['similarity_score'] = 0.0
                
                return original_results
            except Exception as fallback_e:
                print(f"❌ 备用方法也失败: {fallback_e}", flush=True)
                return []
        
    def _extract_dates_from_text(self, text):
        """
        从文本中提取日期信息
        """
        dates = []
        
        # 匹配年月格式：2025年3月, 2024年12月等
        year_month_pattern = r'(\d{4})年(\d{1,2})月'
        matches = re.findall(year_month_pattern, text)
        for year, month in matches:
            dates.append({
                'type': 'year_month',
                'year': int(year),
                'month': int(month)
            })
        
        # 匹配单独的月份：3月, 12月等
        month_only_pattern = r'(\d{1,2})月'
        if not dates:  # 只有在没找到年月组合时才查找单独月份
            matches = re.findall(month_only_pattern, text)
            for month in matches:
                dates.append({
                    'type': 'month_only',
                    'month': int(month)
                })
        
        # 匹配年份：2025年, 2024年等
        year_only_pattern = r'(\d{4})年'
        if not dates:  # 只有在没找到其他日期格式时才查找单独年份
            matches = re.findall(year_only_pattern, text)
            for year in matches:
                dates.append({
                    'type': 'year_only',
                    'year': int(year)
                })
        
        return dates
    
    def _check_date_match(self, input_dates, stored_dates):
        """
        检查两组日期是否匹配
        """
        if not input_dates or not stored_dates:
            return True  # 如果任一方没有日期信息，则认为匹配
        
        for input_date in input_dates:
            for stored_date in stored_dates:
                # 如果都有年月信息，进行精确匹配
                if (input_date.get('type') == 'year_month' and 
                    stored_date.get('type') == 'year_month'):
                    if (input_date.get('year') == stored_date.get('year') and
                        input_date.get('month') == stored_date.get('month')):
                        return True
                
                # 如果都只有月份信息，进行月份匹配
                elif (input_date.get('type') == 'month_only' and 
                      stored_date.get('type') == 'month_only'):
                    if input_date.get('month') == stored_date.get('month'):
                        return True
                
                # 如果都只有年份信息，进行年份匹配
                elif (input_date.get('type') == 'year_only' and 
                      stored_date.get('type') == 'year_only'):
                    if input_date.get('year') == stored_date.get('year'):
                        return True
                
                # 跨类型匹配：年月 vs 月份
                elif (input_date.get('type') == 'year_month' and 
                      stored_date.get('type') == 'month_only'):
                    if input_date.get('month') == stored_date.get('month'):
                        return True
                
                elif (input_date.get('type') == 'month_only' and 
                      stored_date.get('type') == 'year_month'):
                    if input_date.get('month') == stored_date.get('month'):
                        return True
        
        return False  # 没有找到匹配的日期
        
    def fix_training_data(self):
        """修复训练数据格式问题的方法 - 兼容ChromaDB不同版本"""
        print("正在修复训练数据...")
        try:
            # 方法1: 尝试直接访问collection并删除所有数据
            if hasattr(self, 'collection') and self.collection is not None:
                try:
                    all_data = self.collection.get()
                    if all_data and "ids" in all_data and len(all_data["ids"]) > 0:
                        self.collection.delete(ids=all_data["ids"])
                        print(f"✅ 已清除 {len(all_data['ids'])} 条现有训练数据")
                        return True
                    else:
                        print("ℹ️ 集合为空，无需清理")
                        return True
                except Exception as e:
                    print(f"方法1失败: {e}")
            
            # 方法2: 尝试重新创建集合
            try:
                import chromadb
                if hasattr(self, 'chroma_client'):
                    client = self.chroma_client
                else:
                    client = chromadb.Client()
                
                # 删除现有集合
                try:
                    client.delete_collection("vanna")
                    print("✅ 已删除vanna集合")
                except:
                    print("ℹ️ 集合不存在或已删除")
                
                # 重新创建集合
                self.collection = client.create_collection("vanna")
                # 同时更新sql_collection
                self.sql_collection = self.collection
                print("✅ 已重新创建vanna集合并更新sql_collection")
                return True
                
            except Exception as e:
                print(f"方法2失败: {e}")
            
            # 方法3: 如果有remove_training_data方法，尝试不同的调用方式
            try:
                if hasattr(self, 'remove_training_data'):
                    # 尝试获取所有ID然后删除
                    training_data = self.get_training_data()
                    if training_data and len(training_data) > 0:
                        # 如果训练数据有ID，逐个删除
                        for i, item in enumerate(training_data):
                            if isinstance(item, dict) and 'id' in item:
                                try:
                                    self.remove_training_data(item['id'])
                                except:
                                    pass
                        print("✅ 已逐个删除训练数据")
                        return True
                        
            except Exception as e:
                print(f"方法3失败: {e}")
            
            print("⚠️ 所有清理方法均失败，请手动处理")
            return False
            
        except Exception as e:
            print(f"修复训练数据时出错: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False

    def add_question_sql(self, question: str, sql: str, **kwargs) -> str:
        """
        修复版本的add_question_sql，确保ID唯一性
        """
        # 生成真正唯一的ID
        timestamp = str(time.time())
        unique_content = f"{question}_{sql}_{timestamp}_{uuid.uuid4()}"
        question_sql_id = hashlib.sha256(unique_content.encode()).hexdigest()[:16] + "-sql"
        
        print(f"生成唯一ID: {question_sql_id}")
        
        # 准备文档数据
        document = json.dumps({
            "question": question,
            "sql": sql
        }, ensure_ascii=False)
        
        # 添加到集合
        if not hasattr(self, 'sql_collection') or self.sql_collection is None:
            self.sql_collection = self.collection
        
        # 生成嵌入向量
        embedding = self.generate_embedding(question)
        
        try:
            self.sql_collection.add(
                documents=[document],
                embeddings=[embedding],
                ids=[question_sql_id],
                metadatas=[{"type": "sql"}]
            )
            print(f"✅ 成功添加，ID: {question_sql_id}")
            return question_sql_id
        except Exception as e:
            print(f"❌ 添加失败: {e}")
            # 如果ID冲突，生成新的ID再试一次
            question_sql_id = hashlib.sha256(f"{unique_content}_{uuid.uuid4()}".encode()).hexdigest()[:16] + "-sql"
            print(f"重试新ID: {question_sql_id}")
            self.sql_collection.add(
                documents=[document],
                embeddings=[embedding],
                ids=[question_sql_id],
                metadatas=[{"type": "sql"}]
            )
            return question_sql_id

def main():
    """启动服务的主函数 - 不包含训练代码"""
    try:
        # 初始化 Vanna
        print("\n==== 初始化 Vanna ====")
        vn = MyVanna(config=LLM_CONFIG)

        # 连接 MySQL
        print("连接 MySQL...")
        try:
            vn.connect_to_mysql(**DB_CONFIG)
            print("✅ MySQL连接成功")
        except Exception as e:
            print(f"❌ 连接数据库失败: {str(e)}")
            return

        # 简单检查训练数据
        print("\n==== 检查训练数据 ====")
        try:
            training_data = vn.get_training_data()
            if training_data is None or (isinstance(training_data, pd.DataFrame) and training_data.empty) or len(training_data) == 0:
                print("⚠️  警告：没有发现训练数据。请先运行 vanna_train.py 进行训练。")
            else:
                print(f"✅ 已加载训练数据: {len(training_data)} 条")
                
                # 测试一个配置化的问题
                try:
                    test_question = TRAINING_CONFIG.get('startup_test_question', '当前系统状态')
                    similar_pairs = vn.get_similar_question_sql(test_question, n_results=1)
                    if similar_pairs:
                        best_match = similar_pairs[0]
                        similarity_score = best_match.get('similarity_score', 0)
                        print(f"测试问题匹配成功，相似度: {similarity_score:.4f}")
                        print(f"测试问题: {test_question}")
                    else:
                        print(f"测试问题未找到匹配结果: {test_question}")
                except Exception as e:
                    print(f"测试向量检索失败: {e}")
                    
        except Exception as e:
            print(f"检查训练数据时出错: {str(e)}")

        # 启动服务
        print("\n==== 启动应用服务 ====")
        auth_instance = HybridAuth(
            api_key=AUTH_CONFIG['api_key'], 
            admin_username=AUTH_CONFIG['admin_username'], 
            admin_password=AUTH_CONFIG['admin_password']
        )
        
        app = VannaFlaskApp(
            vn,
            auth=auth_instance,
            debug=APP_CONFIG['debug'],
            allow_llm_to_see_data=APP_CONFIG['allow_llm_to_see_data']
        )
        
        # 设置 Flask session 密钥
        app.flask_app.secret_key = APP_CONFIG['secret_key']

        # 🔧 强制覆盖VannaFlaskApp的认证路由
        print("🔧 强制覆盖认证路由...")
        
        @app.flask_app.route('/auth/login', methods=['GET', 'POST'])
        def custom_login():
            if request.method == 'GET':
                # 返回我们自定义的登录页面
                return auth_instance.login_form()
            else:
                # 处理登录提交
                return auth_instance.login_handler(request)
        
        @app.flask_app.route('/auth/logout', methods=['GET', 'POST'])
        def custom_logout():
            return auth_instance.logout_handler(request)
        
        print("✅ 已覆盖认证路由")

        print(f"🚀 启动服务在 {APP_CONFIG['host']}:{APP_CONFIG['port']}")
        print(f"📝 当前项目: {TRAINING_CONFIG.get('project_name', '未知项目')}")
        print(f"📋 项目描述: {TRAINING_CONFIG.get('description', '无描述')}")
        
        # 显示示例问题
        sample_questions = TRAINING_CONFIG.get('ui_sample_questions', [])
        if sample_questions:
            print("\n💡 可以尝试的示例问题:")
            for i, question in enumerate(sample_questions, 1):
                print(f"   {i}. {question}")
        
        app.run(host=APP_CONFIG['host'], port=APP_CONFIG['port'])
        
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序运行出错: {str(e)}")