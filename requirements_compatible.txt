# Vanna AI 兼容当前环境的依赖配置
# 基于当前已安装包的版本，避免冲突

# Vanna AI 核心依赖
vanna==0.7.9

# 向量数据库（使用当前已安装版本，避免降级）
# chromadb==1.0.13  # 已安装，无需重新安装

# Web 框架
flask==3.1.1
flask-sock==0.7.0
flasgger==0.9.7.1

# 数据处理（使用当前版本，避免降级）
# pandas==2.3.0     # 已安装，版本更新
# numpy==2.0.2      # 已安装，版本匹配

# HTTP 请求（使用当前版本）
# requests==2.32.4  # 已安装，版本稍新

# MySQL 数据库连接器
# pymysql==1.1.1    # 已安装，版本匹配

# 其他必需的依赖
# coloredlogs==15.0.1    # 已安装，版本匹配
# python-dotenv==1.1.1  # 已安装，版本稍新
plotly==6.1.0
tabulate==0.9.0
# PyYAML==6.0.2         # 已安装，版本匹配

# 基础依赖
# click==8.1.8      # 已安装，版本匹配
# tqdm==4.67.1      # 已安装，版本匹配
# rich==14.0.0      # 已安装，版本匹配

# 注释说明：
# 带 # 注释的包表示已在当前环境中安装，无需重新安装
# 只安装缺失的包，避免版本冲突
